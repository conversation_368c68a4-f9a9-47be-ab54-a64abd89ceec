import { TimestampedRoomData } from '@/building-mock-data-generator';

export interface DualAxisLineChartProps {
  data: TimestampedRoomData[];
  metricLeft: keyof TimestampedRoomData['floors'][0]['rooms'][0]['devices']['iaq'];
  metricRight: keyof TimestampedRoomData['floors'][0]['rooms'][0]['devices']['iaq'];
  roomId: string;
  dateRange: { startDate: Date; endDate: Date };
}

export interface ChartDataPoint {
  time: Date;
  left: number | null;
  right: number | null;
  occupancy: 0 | 1 | 2 | 3;
}

export interface OccupancyInfo {
  label: string;
  color: string;
}

export type OccupancyStatus = 0 | 1 | 2 | 3;

export interface ChartAnnotation {
  type: 'box';
  xMin: number;
  xMax: number;
  backgroundColor: string;
  borderWidth: number;
  label: {
    display: boolean;
  };
}
