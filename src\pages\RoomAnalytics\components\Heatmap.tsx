import React from 'react';
import { TimestampedRoomData } from '@/building-mock-data-generator';

type IAQStatus = {
  co2: number;
  temperature?: number;
  humidity?: number;
  pm25?: number;
  noise?: number;
  illuminance?: number;
  pmv?: number;
  online_status: boolean;
};

type RoomDeviceStatus = {
  iaq: IAQStatus;
};

type Room = {
  roomId: string;
  roomType: string;
  devices: RoomDeviceStatus;
};

type Floor = {
  level: number;
  rooms: Room[];
};

type BuildingConfig = {
  floors: Floor[];
};

interface HeatmapProps {
  buildingConfig: BuildingConfig;
  selectedMetric: string;
  selectedRoomType: string;
  valueRange: [number, number];
  data: TimestampedRoomData[];
  floorsCount?: number;
  roomsPerFloor?: number;
  dataFileName?: string;
  onCellClick?: (roomId: string, value: number | null) => void;
}

function getColorForValue(metric: string, value: number, valueRange?: [number, number]): string {
  if (valueRange && (value < valueRange[0] || value > valueRange[1])) {
    return '#ccc';
  }
  switch (metric) {
    case 'co2':
      if (value <= 600) return '#4caf50';
      if (value <= 1000) return '#ffeb3b';
      if (value <= 1500) return '#ff9800';
      return '#f44336';
    case 'temperature':
      if (value <= 22) return '#4caf50';
      if (value <= 26) return '#ffeb3b';
      if (value <= 30) return '#ff9800';
      return '#f44336';
    case 'humidity':
      if (value <= 40) return '#f44336';
      if (value <= 60) return '#4caf50';
      return '#ffeb3b';
    case 'pm25':
      if (value <= 12) return '#4caf50';
      if (value <= 35) return '#ffeb3b';
      if (value <= 55) return '#ff9800';
      return '#f44336';
    case 'noise':
      if (value <= 50) return '#4caf50';
      if (value <= 65) return '#ffeb3b';
      if (value <= 80) return '#ff9800';
      return '#f44336';
    case 'illuminance':
      if (value >= 300 && value <= 500) return '#4caf50';
      if (value < 300) return '#ff9800';
      return '#ffeb3b';
    case 'pmv':
      if (value >= -0.5 && value <= 0.5) return '#4caf50';
      if (value >= -1 && value <= 1) return '#ffeb3b';
      return '#f44336';
    default:
      return '#ccc';
  }
}

export const Heatmap: React.FC<HeatmapProps> = ({
  buildingConfig,
  selectedMetric,
  selectedRoomType,
  floorsCount = 14,
  roomsPerFloor = 30,
  data,
  valueRange,
  onCellClick,
}) => {
  const formatRoomNumber = (num: number) => (num < 10 ? `0${num}` : `${num}`);

  // 🔢 Compute average values per room for the selected metric
  const roomAverages: Record<string, number> = {};
  const sumMap: Record<string, number> = {};
  const countMap: Record<string, number> = {};

  for (const entry of data) {
    for (const floor of entry.floors) {
      for (const room of floor.rooms) {
        const { roomId, devices: status } = room;
        const metricValue = selectedMetric in status.iaq 
          ? status.iaq[selectedMetric as keyof typeof status.iaq] 
          : undefined;
        if (typeof metricValue === 'number') {
          sumMap[roomId] = (sumMap[roomId] || 0) + metricValue;
          countMap[roomId] = (countMap[roomId] || 0) + 1;
        }
      }
    }
  }

  for (const roomId in sumMap) {
    roomAverages[roomId] = sumMap[roomId] / countMap[roomId];
  }

  return (
    <div className="w-full h-full">
      <div className="flex flex-col gap-2 max-h-[500px] overflow-y-auto pr-2 h-full">
        {Array.from({ length: floorsCount }, (_, floorIdx) => {
          const floorNumber = floorIdx + 1;
          const floor = buildingConfig.floors.find(f => f.level === floorNumber);

          return (
            <div key={`floor-${floorNumber}`} className="flex flex-col gap-1">
              <div className="flex items-center gap-2">
                <div className="flex flex-wrap gap-[2px] flex-1">
                  {Array.from({ length: roomsPerFloor }, (_, roomIdx) => {
                    const roomNumStr = formatRoomNumber(roomIdx + 1);
                    const roomId = `${floorNumber}${roomNumStr}`;

                    const room = floor?.rooms.find(r => r.roomId === roomId);

                    if (!room || (selectedRoomType !== 'All' && room.roomType !== selectedRoomType)) {
                      return (
                        <div
                          key={`cell-${roomId}`}
                          title={`Room ${roomId}\nFiltered Out`}
                          className="w-4 h-4 rounded bg-gray-300 shadow-inner"
                        />
                      );
                    }

                    const value = roomAverages[roomId] ?? null;
                    const hasData = value !== null;

                    const backgroundColor =
                      hasData
                        ? getColorForValue(selectedMetric, value, valueRange)
                        : '#ccc';

                    return (
                      <div
                        key={`cell-${roomId}`}
                        title={
                          hasData
                            ? `Room ${roomId}\nAvg ${selectedMetric.toUpperCase()}: ${value.toFixed(1)}`
                            : `Room ${roomId}\nNo data`
                        }
                        className={`w-4 h-4 rounded shadow-inner flex items-center justify-center text-[8px] select-none ${
                          hasData ? 'cursor-pointer' : 'cursor-not-allowed'
                        }`}
                        style={{
                          backgroundColor,
                          color:
                            value !== null &&
                            (selectedMetric === 'co2' || selectedMetric === 'temperature') &&
                            value > 1000
                              ? '#fff'
                              : '#000',
                        }}
                        onClick={() => {
                          if (hasData && onCellClick) {
                            onCellClick(roomId, value);
                          }
                        }}
                      >
                        {hasData && value !== null ? value.toFixed(0) : ''}
                      </div>
                    );
                  })}
                </div>
                <div className="w-3 text-xs font-semibold text-gray-700 text-right pr-2">
                  {floorNumber}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default Heatmap;
