import { OccupancyStatus, OccupancyInfo } from '../types/chart.types';

/**
 * Get occupancy information including label and color for a given status
 */
export const getOccupancyInfo = (status: OccupancyStatus): OccupancyInfo => {
  switch (status) {
    case 0:
      return { label: 'Vacant', color: 'rgba(34, 197, 94, 0.2)' }; // Green - room is empty
    case 1:
      return { label: 'Low Occupancy', color: 'rgba(34, 197, 94, 0.1)' }; // Light green
    case 2:
      return { label: 'Medium Occupancy', color: 'rgba(251, 191, 36, 0.2)' }; // Yellow/amber
    case 3:
      return { label: 'High Occupancy', color: 'rgba(239, 68, 68, 0.2)' }; // Red - room is full
    default:
      return { label: 'Unknown', color: 'rgba(156, 163, 175, 0.2)' }; // Gray
  }
};

/**
 * Get all occupancy statuses with their info for legend display
 */
export const getAllOccupancyInfo = (): Array<{ status: OccupancyStatus; info: OccupancyInfo }> => {
  return [
    { status: 0, info: getOccupancyInfo(0) },
    { status: 1, info: getOccupancyInfo(1) },
    { status: 2, info: getOccupancyInfo(2) },
    { status: 3, info: getOccupancyInfo(3) },
  ];
};

/**
 * Get simplified occupancy categories for legend
 */
export const getSimplifiedOccupancyCategories = () => [
  { label: 'Vacant/Low', color: 'rgba(34, 197, 94, 0.4)' },
  { label: 'Medium', color: 'rgba(251, 191, 36, 0.4)' },
  { label: 'High Occupancy', color: 'rgba(239, 68, 68, 0.4)' },
];
