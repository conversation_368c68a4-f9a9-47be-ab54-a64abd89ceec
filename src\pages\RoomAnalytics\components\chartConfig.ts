import { ChartDataPoint, getOccupancyInfo } from './chartDataProcessor';

/**
 * Create background color zones based on occupancy
 */
export const createOccupancyZones = (data: ChartDataPoint[]) => {
  const zones: any[] = [];

  for (let i = 0; i < data.length; i++) {
    const currentPoint = data[i];
    const nextPoint = data[i + 1];

    if (currentPoint && nextPoint) {
      const occupancyInfo = getOccupancyInfo(currentPoint.occupancy);

      zones.push({
        type: 'box',
        xMin: i,
        xMax: i + 1,
        backgroundColor: occupancyInfo.color,
        borderWidth: 0,
        label: {
          display: false,
        },
      });
    }
  }

  return zones;
};

/**
 * Create chart data configuration for Chart.js
 */
export const createChartData = (
  labels: string[],
  data: ChartDataPoint[],
  metricLeft: string,
  metricRight: string
) => ({
  labels,
  datasets: [
    {
      label: metricLeft.toUpperCase(),
      data: data.map((d) => d.left),
      borderColor: 'rgba(75,192,192,1)',
      yAxisID: 'y-left',
      tension: 0.4,
      spanGaps: true,
    },
    {
      label: metricRight.toUpperCase(),
      data: data.map((d) => d.right),
      borderColor: 'rgba(255,99,132,1)',
      yAxisID: 'y-right',
      tension: 0.4,
      spanGaps: true,
    },
  ],
});

/**
 * Create chart options configuration for Chart.js
 */
export const createChartOptions = (
  roomId: string,
  metricLeft: string,
  metricRight: string,
  occupancyZones: any[]
) => ({
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    mode: 'index' as const,
    intersect: false,
  },
  stacked: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
    title: {
      display: true,
      text: [
        `Room ${roomId} - Metrics: ${metricLeft.toUpperCase()} & ${metricRight.toUpperCase()}`,
        'Background Colors: Green=Vacant/Low, Yellow=Medium, Red=High Occupancy'
      ],
    },
    annotation: {
      annotations: occupancyZones,
    },
  },
  scales: {
    x: {
      title: {
        display: true,
        text: 'Time (6-hour intervals)',
      },
      ticks: {
        maxTicksLimit: 12, // Show up to 12 labels for better readability
        maxRotation: 45, // Rotate labels if needed
      },
    },
    'y-left': {
      type: 'linear' as const,
      position: 'left' as const,
      beginAtZero: true,
      title: {
        display: true,
        text: metricLeft.toUpperCase(),
      },
    },
    'y-right': {
      type: 'linear' as const,
      position: 'right' as const,
      beginAtZero: true,
      title: {
        display: true,
        text: metricRight.toUpperCase(),
      },
      grid: {
        drawOnChartArea: false,
      },
    },
  },
});
