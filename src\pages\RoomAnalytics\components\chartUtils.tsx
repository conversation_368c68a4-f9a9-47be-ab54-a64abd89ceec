import React from 'react';
import { TimestampedRoomData } from '@/building-mock-data-generator';

// Types
export interface ChartDataPoint {
  time: Date;
  left: number | null;
  right: number | null;
  occupancy: 0 | 1 | 2 | 3;
}

export type OccupancyStatus = 0 | 1 | 2 | 3;

// Constants
const SIX_HOUR_INTERVAL = 6 * 60 * 60 * 1000; // 6 hours in milliseconds
const THREE_HOUR_TOLERANCE = 3 * 60 * 60 * 1000; // 3 hours in milliseconds

// Occupancy utilities
export const getOccupancyInfo = (status: OccupancyStatus) => {
  switch (status) {
    case 0:
      return { label: 'Vacant', color: 'rgba(34, 197, 94, 0.2)' }; // Green
    case 1:
      return { label: 'Low Occupancy', color: 'rgba(34, 197, 94, 0.1)' }; // Light green
    case 2:
      return { label: 'Medium Occupancy', color: 'rgba(251, 191, 36, 0.2)' }; // Yellow
    case 3:
      return { label: 'High Occupancy', color: 'rgba(239, 68, 68, 0.2)' }; // Red
    default:
      return { label: 'Unknown', color: 'rgba(156, 163, 175, 0.2)' }; // <PERSON>
  }
};

// Data processing functions
export const filterRoomData = (
  data: TimestampedRoomData[],
  roomId: string,
  dateRange: { startDate: Date; endDate: Date },
  metricLeft: keyof TimestampedRoomData['floors'][0]['rooms'][0]['devices']['iaq'],
  metricRight: keyof TimestampedRoomData['floors'][0]['rooms'][0]['devices']['iaq']
): ChartDataPoint[] => {
  return data
    .filter(
      (entry) =>
        new Date(entry.timestamp) >= dateRange.startDate &&
        new Date(entry.timestamp) <= dateRange.endDate
    )
    .map((entry) => {
      const roomData = entry.floors
        .flatMap((f) => f.rooms)
        .find((r) => r.roomId === roomId);

      return roomData
        ? {
            time: new Date(entry.timestamp),
            left: roomData.devices.iaq[metricLeft] ?? null,
            right: roomData.devices.iaq[metricRight] ?? null,
            occupancy: roomData.devices.occupancy_status,
          }
        : null;
    })
    .filter(Boolean) as ChartDataPoint[];
};

export const sampleDataAtSixHourIntervals = (
  allData: ChartDataPoint[],
  dateRange: { startDate: Date; endDate: Date }
): ChartDataPoint[] => {
  const filteredData: ChartDataPoint[] = [];

  if (allData.length === 0) {
    return filteredData;
  }

  const startTime = new Date(dateRange.startDate);
  startTime.setHours(Math.floor(startTime.getHours() / 6) * 6, 0, 0, 0);

  let currentTime = startTime.getTime();
  const endTime = dateRange.endDate.getTime();

  while (currentTime <= endTime) {
    const targetTime = new Date(currentTime);
    const closestDataPoint = allData.reduce((closest, current) => {
      const currentDiff = Math.abs(current.time.getTime() - currentTime);
      const closestDiff = Math.abs(closest.time.getTime() - currentTime);
      return currentDiff < closestDiff ? current : closest;
    });

    const timeDiff = Math.abs(closestDataPoint.time.getTime() - currentTime);
    if (timeDiff <= THREE_HOUR_TOLERANCE) {
      filteredData.push({
        time: targetTime,
        left: closestDataPoint.left,
        right: closestDataPoint.right,
        occupancy: closestDataPoint.occupancy,
      });
    }

    currentTime += SIX_HOUR_INTERVAL;
  }

  return filteredData;
};

export const formatTimeLabels = (data: ChartDataPoint[]): string[] => {
  return data.map((d) =>
    d.time.toLocaleString('en-GB', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  );
};

export const createOccupancyZones = (data: ChartDataPoint[]) => {
  const zones: any[] = [];

  for (let i = 0; i < data.length; i++) {
    const currentPoint = data[i];
    const nextPoint = data[i + 1];

    if (currentPoint && nextPoint) {
      const occupancyInfo = getOccupancyInfo(currentPoint.occupancy);

      zones.push({
        type: 'box',
        xMin: i,
        xMax: i + 1,
        backgroundColor: occupancyInfo.color,
        borderWidth: 0,
        label: {
          display: false,
        },
      });
    }
  }

  return zones;
};

// Chart configuration functions
export const createChartData = (
  labels: string[],
  data: ChartDataPoint[],
  metricLeft: string,
  metricRight: string
) => ({
  labels,
  datasets: [
    {
      label: metricLeft.toUpperCase(),
      data: data.map((d) => d.left),
      borderColor: 'rgba(75,192,192,1)',
      yAxisID: 'y-left',
      tension: 0.4,
      spanGaps: true,
    },
    {
      label: metricRight.toUpperCase(),
      data: data.map((d) => d.right),
      borderColor: 'rgba(255,99,132,1)',
      yAxisID: 'y-right',
      tension: 0.4,
      spanGaps: true,
    },
  ],
});

export const createChartOptions = (
  roomId: string,
  metricLeft: string,
  metricRight: string,
  occupancyZones: any[]
) => ({
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    mode: 'index' as const,
    intersect: false,
  },
  stacked: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
    title: {
      display: true,
      text: [
        `Room ${roomId} - Metrics: ${metricLeft.toUpperCase()} & ${metricRight.toUpperCase()}`,
        'Background Colors: Green=Vacant/Low, Yellow=Medium, Red=High Occupancy'
      ],
    },
    annotation: {
      annotations: occupancyZones,
    },
  },
  scales: {
    x: {
      title: {
        display: true,
        text: 'Time (6-hour intervals)',
      },
      ticks: {
        maxTicksLimit: 12,
        maxRotation: 45,
      },
    },
    'y-left': {
      type: 'linear' as const,
      position: 'left' as const,
      beginAtZero: true,
      title: {
        display: true,
        text: metricLeft.toUpperCase(),
      },
    },
    'y-right': {
      type: 'linear' as const,
      position: 'right' as const,
      beginAtZero: true,
      title: {
        display: true,
        text: metricRight.toUpperCase(),
      },
      grid: {
        drawOnChartArea: false,
      },
    },
  },
});

// Debug logging
export const logChartDebugInfo = (
  roomId: string,
  totalDataEntries: number,
  allFilteredDataLength: number,
  sixHourSampledDataLength: number,
  dateRange: { startDate: Date; endDate: Date },
  sampleData: ChartDataPoint[]
): void => {
  console.log('DualAxisLineChart Debug:', {
    roomId,
    totalDataEntries,
    allFilteredDataLength,
    sixHourSampledDataLength,
    dateRange,
    sampleSixHourData: sampleData.slice(0, 3),
    sixHourIntervalMs: SIX_HOUR_INTERVAL,
    occupancyData: sampleData.map(d => ({
      time: d.time.toISOString(),
      occupancy: d.occupancy,
      occupancyInfo: getOccupancyInfo(d.occupancy)
    }))
  });
};

// React components
export const OccupancyLegend = () => {
  const categories = [
    { label: 'Vacant/Low', color: 'rgba(34, 197, 94, 0.4)' },
    { label: 'Medium', color: 'rgba(251, 191, 36, 0.4)' },
    { label: 'High Occupancy', color: 'rgba(239, 68, 68, 0.4)' },
  ];

  return (
    <div className="mt-2 flex flex-wrap gap-4 text-xs justify-center">
      {categories.map((category, index) => (
        <div key={index} className="flex items-center gap-1">
          <div
            className="w-3 h-3 rounded"
            style={{ backgroundColor: category.color }}
          />
          <span>{category.label}</span>
        </div>
      ))}
    </div>
  );
};

export const NoDataMessage = ({
  roomId,
  dateRange
}: {
  roomId: string;
  dateRange: { startDate: Date; endDate: Date }
}) => {
  return (
    <div className="h-64 flex items-center justify-center border rounded-lg bg-gray-50">
      <div className="text-center text-gray-500">
        <p className="text-lg font-medium">No data available</p>
        <p className="text-sm">
          No data found for Room {roomId} in the selected date range.
        </p>
        <p className="text-xs mt-2">
          Date range: {dateRange.startDate.toLocaleDateString()} -{' '}
          {dateRange.endDate.toLocaleDateString()}
        </p>
      </div>
    </div>
  );
};
