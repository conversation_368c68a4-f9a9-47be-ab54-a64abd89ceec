import React from 'react';

export type YAxisMetric =
  | 'co2'
  | 'temperature'
  | 'humidity'
  | 'pm25'
  | 'noise'
  | 'illuminance'
  | 'pmv';

interface YAxisDropdownProps {
  label: string;
  selectedMetric: YAxisMetric;
  onChange: (metric: YAxisMetric) => void;
}

const METRIC_LABELS: Record<YAxisMetric, string> = {
  co2: 'CO₂',
  temperature: 'Temperature',
  humidity: 'Humidity',
  pm25: 'PM2.5',
  noise: 'Noise',
  illuminance: 'Illuminance',
  pmv: 'PMV',
};

export const YAxisDropdown: React.FC<YAxisDropdownProps> = ({
  label,
  selectedMetric,
  onChange,
}) => {
  return (
    <div className="flex flex-col items-start gap-1">
      <label className="text-xs font-medium text-gray-600">{label}</label>
      <select
        value={selectedMetric}
        onChange={(e) => onChange(e.target.value as YAxisMetric)}
        className="px-2 py-1 text-sm border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        {Object.entries(METRIC_LABELS).map(([key, label]) => (
          <option key={key} value={key}>
            {label}
          </option>
        ))}
      </select>
    </div>
  );
};
