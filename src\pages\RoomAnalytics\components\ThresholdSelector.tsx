import React from 'react';
import IAQFilterDropdown from './IAQFilterDropDown';
import RangeSelector from './RangeSelector';

// Define metric ranges configuration
const IAQ_METRIC_RANGES: Record<string, { min: number; max: number; step?: number; unit: string }> = {
  'co2': { min: 400, max: 2000, step: 50, unit: 'ppm' },
  'temperature': { min: 10, max: 40, step: 1, unit: '°C' },
  'pm25': { min: 0, max: 150, step: 5, unit: 'µg/m³' },
  'humidity': { min: 0, max: 100, step: 5, unit: '%' },
  'noise': { min: 0, max: 100, step: 5, unit: 'dB' },
  'illuminance': { min: 0, max: 1000, step: 50, unit: 'lux' },
  'pmv': { min: -3, max: 3, step: 0.1, unit: '' },
};

interface ThresholdSelectorProps {
  selectedMetric: string;
  valueRange: [number, number];
  onMetricChange: (metric: string) => void;
  onRangeChange: (range: [number, number]) => void;
}

const ThresholdSelector: React.FC<ThresholdSelectorProps> = ({
  selectedMetric,
  valueRange,
  onMetricChange,
  onRangeChange,
}) => {
  return (
    <div className="bg-gray-100 rounded-lg p-3 space-y-3 w-full">
      <h3 className="text-lg font-medium text-gray-800">Threshold Selector</h3>

      <div className="flex gap-3 items-center">
        <div className="flex-1 w-full">
          <IAQFilterDropdown
            selectedMetric={selectedMetric}
            onSelect={onMetricChange}
          />
        </div>

        <div className="flex">
          <RangeSelector
            min={IAQ_METRIC_RANGES[selectedMetric]?.min || 0}
            max={IAQ_METRIC_RANGES[selectedMetric]?.max || 100}
            step={IAQ_METRIC_RANGES[selectedMetric]?.step || 1}
            value={valueRange}
            unit={IAQ_METRIC_RANGES[selectedMetric]?.unit || ''}
            onChange={onRangeChange}
            formatUnit={(value, unit) => `${value} ${unit}`}
          />
        </div>
      </div>
    </div>
  );
};

export default ThresholdSelector;
