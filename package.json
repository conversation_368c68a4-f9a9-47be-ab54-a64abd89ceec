{"name": "frontend", "private": true, "version": "0.0.0", "type": "commonjs", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ai-sdk/ui-utils": "^1.1.17", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@shadcn/ui": "^0.0.4", "@supabase/supabase-js": "^2.48.1", "@tanstack/react-table": "^8.20.6", "@types/axios": "^0.9.36", "@types/file-saver": "^2.0.7", "@vercel/analytics": "^1.3.1", "@vercel/kv": "^2.0.0", "ag-grid-react": "^33.0.4", "ai": "^4.0.2", "axios": "^1.7.9", "chart.js": "^4.4.9", "chartjs-plugin-annotation": "^3.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "echarts-gl": "^2.0.9", "file-saver": "^2.0.5", "framer-motion": "^11.18.2", "geist": "^1.3.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jszip": "^3.10.1", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "luxon": "^3.6.1", "next-themes": "^0.4.4", "papaparse": "^5.5.2", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-date-range": "^2.0.1", "react-datepicker": "^8.2.1", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-icons": "^5.5.0", "react-markdown": "^9.0.1", "react-router-dom": "^7.1.5", "react-tsparticles": "^2.12.2", "remark-gfm": "^4.0.0", "simple-statistics": "^7.8.8", "sonner": "^1.5.0", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "tsparticles": "^3.8.1", "tsparticles-slim": "^2.12.0", "usehooks-ts": "^3.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@types/lodash": "^4.17.16", "@types/luxon": "^3.6.2", "@types/node": "^20.9.0", "@types/papaparse": "^5.3.15", "@types/react": "^18.2.15", "@types/react-date-range": "^1.4.10", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.16", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.0.2", "vite": "^6.2.0", "vite-plugin-svgr": "^4.3.0"}}