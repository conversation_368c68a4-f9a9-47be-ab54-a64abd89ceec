import * as React from 'react';
import * as SliderPrimitive from '@radix-ui/react-slider';
import { createPortal } from 'react-dom';

interface RangeSelectorProps {
  label?: string;
  min: number;
  max: number;
  step?: number;
  value: [number, number];
  onChange: (value: [number, number]) => void;
  unit?: string;
  formatUnit?: (value: number, unit: string) => string;
}

const RangeSelector: React.FC<RangeSelectorProps> = ({
  label,
  min,
  max,
  step = 1,
  value,
  onChange,
  unit = '',
  formatUnit,
}) => {
  const [inputMin, inputMax] = value;
  const [isOpen, setIsOpen] = React.useState(false);
  const containerRef = React.useRef<HTMLDivElement>(null);
  const buttonRef = React.useRef<HTMLButtonElement>(null);
  const [dropdownPosition, setDropdownPosition] = React.useState<{ top: number; left: number; width: number } | null>(null);

  const formattedLabel = formatUnit
    ? `${formatUnit(inputMin, unit)} — ${formatUnit(inputMax, unit)}`
    : `${inputMin} ${unit} — ${inputMax} ${unit}`;

  // Calculate dropdown position when opening
  React.useEffect(() => {
    if (isOpen && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const dropdownWidth = Math.max(rect.width, 250);
      const dropdownHeight = 60; // Approximate height of the dropdown

      // Calculate initial position
      let top = rect.bottom + window.scrollY + 4;
      let left = rect.left + window.scrollX;

      // Check if dropdown would overflow viewport horizontally
      if (left + dropdownWidth > window.innerWidth) {
        left = window.innerWidth - dropdownWidth - 10; // 10px margin from edge
      }

      // Check if dropdown would overflow viewport vertically
      if (rect.bottom + dropdownHeight > window.innerHeight) {
        // Position above the button instead
        top = rect.top + window.scrollY - dropdownHeight - 4;
      }

      // Ensure minimum distance from edges
      left = Math.max(10, left);
      top = Math.max(10, top);

      setDropdownPosition({
        top,
        left,
        width: dropdownWidth
      });
    }
  }, [isOpen]);

  // Close slider when clicking outside the container
  React.useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    }
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  return (
    <>
      <div className={`relative ${label ? 'space-y-2' : ''}`} ref={containerRef}>
        {label && <label className="block font-medium">{label}</label>}

        {/* Button showing current range */}
        <button
          ref={buttonRef}
          type="button"
          className="w-full border rounded px-3 py-2 text-left text-sm bg-white hover:bg-muted transition"
          onClick={() => setIsOpen((prev) => !prev)}
        >
          {formattedLabel}
        </button>
      </div>

      {/* Portal-rendered dropdown that overlaps other components */}
      {isOpen && dropdownPosition && createPortal(
        <div
          className="fixed px-3 py-2 border rounded bg-white shadow-lg"
          style={{
            top: dropdownPosition.top,
            left: dropdownPosition.left,
            width: dropdownPosition.width,
            zIndex: 9000,
            minWidth: '250px'
          }}
        >
          <SliderPrimitive.Root
            className="relative flex w-full touch-none select-none items-center"
            min={min}
            max={max}
            step={step}
            value={[inputMin, inputMax]}
            onValueChange={(val) => {
              if (val.length === 2) onChange([val[0], val[1]]);
            }}
            aria-label="Range slider"
          >
            <SliderPrimitive.Track className="relative h-2 w-full grow overflow-hidden rounded-full bg-muted">
              <SliderPrimitive.Range className="absolute h-full bg-primary" />
            </SliderPrimitive.Track>
            <SliderPrimitive.Thumb
              className="block h-5 w-5 rounded-full bg-primary shadow transition hover:scale-110 focus:outline-none"
              aria-label="Minimum value thumb"
            />
            <SliderPrimitive.Thumb
              className="block h-5 w-5 rounded-full bg-primary shadow transition hover:scale-110 focus:outline-none"
              aria-label="Maximum value thumb"
            />
          </SliderPrimitive.Root>
        </div>,
        document.body
      )}
    </>
  );
};

export default RangeSelector;
