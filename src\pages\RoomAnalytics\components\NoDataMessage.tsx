import React from 'react';

interface NoDataMessageProps {
  roomId: string;
  dateRange: { startDate: Date; endDate: Date };
}

/**
 * Component that displays a message when no data is available for the selected room and date range
 */
export const NoDataMessage: React.FC<NoDataMessageProps> = ({ roomId, dateRange }) => {
  return (
    <div className="h-64 flex items-center justify-center border rounded-lg bg-gray-50">
      <div className="text-center text-gray-500">
        <p className="text-lg font-medium">No data available</p>
        <p className="text-sm">
          No data found for Room {roomId} in the selected date range.
        </p>
        <p className="text-xs mt-2">
          Date range: {dateRange.startDate.toLocaleDateString()} -{' '}
          {dateRange.endDate.toLocaleDateString()}
        </p>
      </div>
    </div>
  );
};
