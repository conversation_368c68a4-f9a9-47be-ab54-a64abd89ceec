import React from 'react';
import { getSimplifiedOccupancyCategories } from '../utils/occupancy.utils';

/**
 * Component that displays the occupancy color legend below the chart
 */
export const OccupancyLegend: React.FC = () => {
  const categories = getSimplifiedOccupancyCategories();

  return (
    <div className="mt-2 flex flex-wrap gap-4 text-xs justify-center">
      {categories.map((category, index) => (
        <div key={index} className="flex items-center gap-1">
          <div 
            className="w-3 h-3 rounded" 
            style={{ backgroundColor: category.color }}
          />
          <span>{category.label}</span>
        </div>
      ))}
    </div>
  );
};
