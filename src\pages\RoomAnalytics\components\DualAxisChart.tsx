import React from 'react';
import { Line } from 'react-chartjs-2';

// Import configuration and setup
import '../config/chartSetup';

// Import types
import { DualAxisLineChartProps } from '../types/chart.types';

// Import utilities
import {
  filterRoomData,
  sampleDataAtSixHourIntervals,
  formatTimeLabels,
  createOccupancyZones,
  logChartDebugInfo,
} from '../utils/chartData.utils';

// Import configuration
import { createChartData, createChartOptions } from '../config/chartConfig';

// Import components
import { OccupancyLegend } from './OccupancyLegend';
import { NoDataMessage } from './NoDataMessage';

export const DualAxisLineChart: React.FC<DualAxisLineChartProps> = ({
  data,
  metricLeft,
  metricRight,
  roomId,
  dateRange,
}) => {
  // Process data using utility functions
  const allFilteredData = filterRoomData(data, roomId, dateRange, metricLeft, metricRight);
  const filteredData = sampleDataAtSixHourIntervals(allFilteredData, dateRange);

  // Debug logging
  logChartDebugInfo(
    roomId,
    data.length,
    allFilteredData.length,
    filteredData.length,
    dateRange,
    filteredData
  );

  // Generate chart elements using utility functions
  const labels = formatTimeLabels(filteredData);
  const occupancyZones = createOccupancyZones(filteredData);

  // Create chart configuration using utility functions
  const chartData = createChartData(labels, filteredData, metricLeft, metricRight);
  const options = createChartOptions(roomId, metricLeft, metricRight, occupancyZones);

  // Show no data message if no data is available
  if (filteredData.length === 0) {
    return <NoDataMessage roomId={roomId} dateRange={dateRange} />;
  }

  return (
    <div className="w-full h-full">
      <Line data={chartData} options={options} />
      <OccupancyLegend />
    </div>
  );
};
