import React from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { TimestampedRoomData } from '@/building-mock-data-generator';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface DualAxisLineChartProps {
  data: TimestampedRoomData[];
  metricLeft: keyof TimestampedRoomData['floors'][0]['rooms'][0]['devices']['iaq'];
  metricRight: keyof TimestampedRoomData['floors'][0]['rooms'][0]['devices']['iaq'];
  roomId: string;
  dateRange: { startDate: Date; endDate: Date };
}

export const DualAxisLineChart: React.FC<DualAxisLineChartProps> = ({
  data,
  metricLeft,
  metricRight,
  roomId,
  dateRange,
}) => {
  const filteredData = data
    .filter(
      (entry) =>
        new Date(entry.timestamp) >= dateRange.startDate &&
        new Date(entry.timestamp) <= dateRange.endDate
    )
    .map((entry) => {
      const roomData = entry.floors
        .flatMap((f) => f.rooms)
        .find((r) => r.roomId === roomId);

      return roomData
        ? {
            time: new Date(entry.timestamp),
            left: roomData.devices.iaq[metricLeft] ?? null,
            right: roomData.devices.iaq[metricRight] ?? null,
          }
        : null;
    })
    .filter(Boolean) as { time: Date; left: number | null; right: number | null }[];

  // Debugging output
  console.log('DualAxisLineChart Debug:', {
    roomId,
    totalDataEntries: data.length,
    filteredDataLength: filteredData.length,
    dateRange,
    sampleFilteredData: filteredData.slice(0, 3)
  });

  // ✅ FIX: Include all labels, use chart options to limit ticks
  const labels = filteredData.map((d) =>
    d.time.toLocaleString('en-GB', { dateStyle: 'short', timeStyle: 'short' })
  );

  const chartData = {
    labels,
    datasets: [
      {
        label: metricLeft.toUpperCase(),
        data: filteredData.map((d) => d.left),
        borderColor: 'rgba(75,192,192,1)',
        yAxisID: 'y-left',
        tension: 0.4,
        spanGaps: true,
      },
      {
        label: metricRight.toUpperCase(),
        data: filteredData.map((d) => d.right),
        borderColor: 'rgba(255,99,132,1)',
        yAxisID: 'y-right',
        tension: 0.4,
        spanGaps: true,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    stacked: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: `Room ${roomId} - Metrics: ${metricLeft.toUpperCase()} & ${metricRight.toUpperCase()}`,
      },
    },
    scales: {
      x: {
        ticks: {
          maxTicksLimit: 10, // ✅ Limit number of X-axis labels to avoid clutter
        },
      },
      'y-left': {
        type: 'linear' as const,
        position: 'left' as const,
        beginAtZero: true,
        title: {
          display: true,
          text: metricLeft.toUpperCase(),
        },
      },
      'y-right': {
        type: 'linear' as const,
        position: 'right' as const,
        beginAtZero: true,
        title: {
          display: true,
          text: metricRight.toUpperCase(),
        },
        grid: {
          drawOnChartArea: false,
        },
      },
    },
  };

  if (filteredData.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center border rounded-lg bg-gray-50">
        <div className="text-center text-gray-500">
          <p className="text-lg font-medium">No data available</p>
          <p className="text-sm">
            No data found for Room {roomId} in the selected date range.
          </p>
          <p className="text-xs mt-2">
            Date range: {dateRange.startDate.toLocaleDateString()} -{' '}
            {dateRange.endDate.toLocaleDateString()}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full">
      <Line data={chartData} options={options} />
    </div>
  );
};
