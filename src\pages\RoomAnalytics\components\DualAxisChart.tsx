import React from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { TimestampedRoomData } from '@/building-mock-data-generator';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface DualAxisLineChartProps {
  data: TimestampedRoomData[];
  metricLeft: keyof TimestampedRoomData['floors'][0]['rooms'][0]['devices']['iaq'];
  metricRight: keyof TimestampedRoomData['floors'][0]['rooms'][0]['devices']['iaq'];
  roomId: string;
  dateRange: { startDate: Date; endDate: Date };  // Add dateRange prop
}

export const DualAxisLineChart: React.FC<DualAxisLineChartProps> = ({
  data,
  metricLeft,
  metricRight,
  roomId,
  dateRange,
}) => {
  // Filter and map data within dateRange
  const filteredData = data
    .filter(
      (entry) =>
        new Date(entry.timestamp) >= dateRange.startDate &&
        new Date(entry.timestamp) <= dateRange.endDate
    )
    .map((entry) => {
      const roomData = entry.floors
        .flatMap((f) => f.rooms)
        .find((r) => r.roomId === roomId && r.devices.iaq.online_status);

      return roomData
        ? {
            time: new Date(entry.timestamp),
            left: roomData.devices.iaq[metricLeft] ?? null,
            right: roomData.devices.iaq[metricRight] ?? null,
          }
        : null;
    })
    .filter(Boolean) as { time: Date; left: number | null; right: number | null }[];

  // Generate labels for X-axis, evenly spaced for clarity
  // If too many points, reduce by picking every nth label
  const labelInterval = Math.max(1, Math.floor(filteredData.length / 10));
  const labels = filteredData
    .filter((_, i) => i % labelInterval === 0)
    .map((d) =>
      d.time.toLocaleString('en-GB', { dateStyle: 'short', timeStyle: 'short' })
    );

  const chartData = {
    labels,
    datasets: [
      {
        label: metricLeft.toUpperCase(),
        data: filteredData.map((d) => d.left),
        borderColor: 'rgba(75,192,192,1)',
        yAxisID: 'y-left',
        tension: 0.4,
        spanGaps: true,
      },
      {
        label: metricRight.toUpperCase(),
        data: filteredData.map((d) => d.right),
        borderColor: 'rgba(255,99,132,1)',
        yAxisID: 'y-right',
        tension: 0.4,
        spanGaps: true,
      },
    ],
  };

  const options = {
    responsive: true,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    stacked: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: `Room ${roomId} - Metrics: ${metricLeft.toUpperCase()} & ${metricRight.toUpperCase()}`,
      },
    },
    scales: {
      'y-left': {
        type: 'linear' as const,
        position: 'left' as const,
        beginAtZero: true,
        title: {
          display: true,
          text: metricLeft.toUpperCase(),
        },
      },
      'y-right': {
        type: 'linear' as const,
        position: 'right' as const,
        beginAtZero: true,
        title: {
          display: true,
          text: metricRight.toUpperCase(),
        },
        grid: {
          drawOnChartArea: false,
        },
      },
    },
  };

  return <Line data={chartData} options={options} />;
};
