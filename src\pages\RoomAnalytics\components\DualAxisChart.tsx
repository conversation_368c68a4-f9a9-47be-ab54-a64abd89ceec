import React from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { TimestampedRoomData } from '@/building-mock-data-generator';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface DualAxisLineChartProps {
  data: TimestampedRoomData[];
  metricLeft: keyof TimestampedRoomData['floors'][0]['rooms'][0]['devices']['iaq'];
  metricRight: keyof TimestampedRoomData['floors'][0]['rooms'][0]['devices']['iaq'];
  roomId: string;
  dateRange: { startDate: Date; endDate: Date };
}

export const DualAxisLineChart: React.FC<DualAxisLineChartProps> = ({
  data,
  metricLeft,
  metricRight,
  roomId,
  dateRange,
}) => {
  // First, get all data within the date range
  const allFilteredData = data
    .filter(
      (entry) =>
        new Date(entry.timestamp) >= dateRange.startDate &&
        new Date(entry.timestamp) <= dateRange.endDate
    )
    .map((entry) => {
      const roomData = entry.floors
        .flatMap((f) => f.rooms)
        .find((r) => r.roomId === roomId);

      return roomData
        ? {
            time: new Date(entry.timestamp),
            left: roomData.devices.iaq[metricLeft] ?? null,
            right: roomData.devices.iaq[metricRight] ?? null,
          }
        : null;
    })
    .filter(Boolean) as { time: Date; left: number | null; right: number | null }[];

  // Sample data at 6-hour intervals (6 hours = 6 * 60 * 60 * 1000 = 21,600,000 milliseconds)
  const sixHourInterval = 6 * 60 * 60 * 1000;
  const filteredData: { time: Date; left: number | null; right: number | null }[] = [];

  if (allFilteredData.length > 0) {
    // Start from the first data point's time, rounded to the nearest 6-hour mark
    const startTime = new Date(dateRange.startDate);
    startTime.setHours(Math.floor(startTime.getHours() / 6) * 6, 0, 0, 0);

    let currentTime = startTime.getTime();
    const endTime = dateRange.endDate.getTime();

    while (currentTime <= endTime) {
      // Find the closest data point to this 6-hour mark (within ±3 hours)
      const targetTime = new Date(currentTime);
      const closestDataPoint = allFilteredData.reduce((closest, current) => {
        const currentDiff = Math.abs(current.time.getTime() - currentTime);
        const closestDiff = Math.abs(closest.time.getTime() - currentTime);
        return currentDiff < closestDiff ? current : closest;
      });

      // Only include if within 3 hours of the target time
      const timeDiff = Math.abs(closestDataPoint.time.getTime() - currentTime);
      if (timeDiff <= 3 * 60 * 60 * 1000) { // 3 hours tolerance
        filteredData.push({
          time: targetTime, // Use the 6-hour mark time for consistent spacing
          left: closestDataPoint.left,
          right: closestDataPoint.right,
        });
      }

      currentTime += sixHourInterval;
    }
  }

  // Debugging output
  console.log('DualAxisLineChart Debug:', {
    roomId,
    totalDataEntries: data.length,
    allFilteredDataLength: allFilteredData.length,
    sixHourSampledDataLength: filteredData.length,
    dateRange,
    sampleSixHourData: filteredData.slice(0, 3),
    sixHourIntervalMs: sixHourInterval
  });

  // Format labels for 6-hour intervals
  const labels = filteredData.map((d) =>
    d.time.toLocaleString('en-GB', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  );

  const chartData = {
    labels,
    datasets: [
      {
        label: metricLeft.toUpperCase(),
        data: filteredData.map((d) => d.left),
        borderColor: 'rgba(75,192,192,1)',
        yAxisID: 'y-left',
        tension: 0.4,
        spanGaps: true,
      },
      {
        label: metricRight.toUpperCase(),
        data: filteredData.map((d) => d.right),
        borderColor: 'rgba(255,99,132,1)',
        yAxisID: 'y-right',
        tension: 0.4,
        spanGaps: true,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    stacked: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: `Room ${roomId} - Metrics: ${metricLeft.toUpperCase()} & ${metricRight.toUpperCase()}`,
      },
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'Time (6-hour intervals)',
        },
        ticks: {
          maxTicksLimit: 12, // Show up to 12 labels for better readability
          maxRotation: 45, // Rotate labels if needed
        },
      },
      'y-left': {
        type: 'linear' as const,
        position: 'left' as const,
        beginAtZero: true,
        title: {
          display: true,
          text: metricLeft.toUpperCase(),
        },
      },
      'y-right': {
        type: 'linear' as const,
        position: 'right' as const,
        beginAtZero: true,
        title: {
          display: true,
          text: metricRight.toUpperCase(),
        },
        grid: {
          drawOnChartArea: false,
        },
      },
    },
  };

  if (filteredData.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center border rounded-lg bg-gray-50">
        <div className="text-center text-gray-500">
          <p className="text-lg font-medium">No data available</p>
          <p className="text-sm">
            No data found for Room {roomId} in the selected date range.
          </p>
          <p className="text-xs mt-2">
            Date range: {dateRange.startDate.toLocaleDateString()} -{' '}
            {dateRange.endDate.toLocaleDateString()}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full">
      <Line data={chartData} options={options} />
    </div>
  );
};
