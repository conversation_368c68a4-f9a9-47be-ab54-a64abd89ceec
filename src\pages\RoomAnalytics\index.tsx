'use client';

import React, { useState, useEffect } from 'react';
import RoomFilterDropdown from './components/RoomFilterDropDown';
import { BUILDING_CONFIG, IAQStatus } from '@/building-mock-data';
import { Heatmap } from './components/Heatmap';
import ThresholdSelector from './components/ThresholdSelector';
import { generateMockHotelData, TimestampedRoomData } from '@/building-mock-data-generator';
import { DatePickerWithRange } from '@/components/ui/date-range-picker';
import { DualAxisLineChart } from './components/DualAxisChart';
import { YAxisDropdown, YAxisMetric } from './components/YAxisDropdown';

const IAQ_METRIC_RANGES: Record<string, { min: number; max: number; step?: number; unit: string }> = {
  co2: { min: 400, max: 2000, step: 50, unit: 'ppm' },
  temperature: { min: 10, max: 40, step: 1, unit: '°C' },
  pm25: { min: 0, max: 150, step: 5, unit: 'µg/m³' },
  humidity: { min: 0, max: 100, step: 5, unit: '%' },
  noise: { min: 0, max: 100, step: 5, unit: 'dB' },
  illuminance: { min: 0, max: 1000, step: 50, unit: 'lux' },
  pmv: { min: -3, max: 3, step: 0.1, unit: '' },
};

const RoomAnalytics: React.FC = () => {
  const [selectedMetric, setSelectedMetric] = useState<string>('co2');
  const [selectedRoomType, setSelectedRoomType] = useState<string>('All');
  const [selectedCell, setSelectedCell] = useState<{ row: number; col: number } | null>(null);
  const [selectedRoomId, setSelectedRoomId] = useState<string | null>(null);
  const [valueRange, setValueRange] = useState<[number, number]>([400, 2000]); // Initialize with CO2 range

  // Date range state - Extended to 3 days to show meaningful 6-hour intervals
  const [dateRange, setDateRange] = useState<{ startDate: Date; endDate: Date }>({
    startDate: new Date('2025-05-30T00:00:00Z'),
    endDate: new Date('2025-06-02T00:00:00Z'),
  });

  const [mockData, setMockData] = useState<TimestampedRoomData[]>([]);

  useEffect(() => {
    if (dateRange.startDate && dateRange.endDate) {
      const data = generateMockHotelData(dateRange.startDate, dateRange.endDate);
      setMockData(data);
    }
  }, [dateRange]);

  useEffect(() => {
    const rangeConfig = IAQ_METRIC_RANGES[selectedMetric];
    if (rangeConfig) {
      setValueRange([rangeConfig.min, rangeConfig.max]);
    }
  }, [selectedMetric]);

  const handleMetricChange = (metric: string) => {
    setSelectedMetric(metric);
  };

  // Y axis metrics for dual axis chart
  const [leftMetric, setLeftMetric] = useState<YAxisMetric>('co2');
  const [rightMetric, setRightMetric] = useState<YAxisMetric>('temperature');

  const handleCellClick = (roomId: string, value: number | null) => {
    console.log('Cell clicked:', { roomId, value, mockDataLength: mockData.length });
    if (!value) return;
    setSelectedRoomId(roomId);
    const floor = parseInt(roomId.slice(0, -2), 10) - 1;
    const room = parseInt(roomId.slice(-2), 10) - 1;
    setSelectedCell({ row: floor, col: room });
    console.log('Selected room set:', { selectedRoomId: roomId, selectedCell: { row: floor, col: room } });
  };

  return (
    <div className="w-full h-[calc(100vh-71px)] bg-background p-6 overflow-hidden">
      <div className="flex h-full gap-4">
        {/* Left side */}
        <div className="flex flex-col w-9/12 gap-4">
          <div className="flex justify-end">
            <DatePickerWithRange
              date={{
                from: dateRange.startDate,
                to: dateRange.endDate,
              }}
              onDateChange={(range) => {
                const startDate = range?.from || null;
                const endDate = range?.to || null;
                if (startDate && endDate) {
                  setDateRange({ startDate, endDate });
                }
              }}
            />
          </div>

          <div className="bg-white rounded-xl shadow p-6 flex-1 overflow-auto h-full">
            {selectedCell ? (
              <>
                <h2 className="text-xl font-medium mb-4">
                  Room {selectedRoomId}
                </h2>
              </>
            ) : (
              <div className="h-full flex items-center justify-center text-gray-400 text-lg">
                Click a cell in the heatmap to see details.
              </div>
            )}

            {/* Dual Axis Chart & Y-axis dropdowns */}
            {selectedRoomId && (
              <div className="mt-6 h-1/2">
                <div className="flex justify-between mb-2 px-4">
                  <YAxisDropdown selectedMetric={leftMetric} onChange={setLeftMetric} label="Left Y-Axis" />
                  <YAxisDropdown selectedMetric={rightMetric} onChange={setRightMetric} label="Right Y-Axis" />
                </div>

                <div className="h-full">
                  <DualAxisLineChart
                    data={mockData}
                    roomId={selectedRoomId}
                    metricLeft={leftMetric as keyof IAQStatus}
                    metricRight={rightMetric as keyof IAQStatus}
                    dateRange={dateRange}
                  />
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Configuration panel */}
        <div className="w-3/12 bg-white rounded-xl shadow p-4 overflow-visible space-y-4 h-full">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-medium">Configuration</h2>
            <p className="text-sm text-gray-500 pl-2">Room Types</p>
            <RoomFilterDropdown selectedRoomType={selectedRoomType} onSelectRoomType={setSelectedRoomType} />
          </div>

          <ThresholdSelector
            selectedMetric={selectedMetric}
            valueRange={valueRange}
            onMetricChange={handleMetricChange}
            onRangeChange={setValueRange}
          />

          {/* Heatmap */}
          <div>
            <Heatmap
              buildingConfig={BUILDING_CONFIG}
              floorsCount={14}
              roomsPerFloor={30}
              data={mockData}
              selectedMetric={selectedMetric}
              selectedRoomType={selectedRoomType}
              onCellClick={handleCellClick}
              valueRange={valueRange}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoomAnalytics;
