// mock-hotel-data-generator.ts

import { RoomDeviceStatus, Room, IAQStatus } from './building-mock-data';

export interface TimestampedRoomData {
  timestamp: string; // ISO string
  floors: {
    level: number;
    rooms: {
      roomId: string;
      roomType: Room['roomType'];
      devices: RoomDeviceStatus;
    }[];
  }[];
}

/**
 * Generate random integer between min and max (inclusive)
 */
function randInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * Generate random float between min and max with decimals
 */
function randFloat(min: number, max: number, decimals = 1): number {
  const factor = 10 ** decimals;
  return Math.round((Math.random() * (max - min) + min) * factor) / factor;
}

/**
 * Generate randomized FCUStatus data
 */
function generateFCUStatus(): RoomDeviceStatus['fcu'] {
  return {
    on_off: randInt(0, 1) as 0 | 1,
    operation_mode: randInt(1, 4) as 1 | 2 | 3 | 4,
    airflow_rate: randInt(1, 7) as 1 | 2 | 3 | 4 | 5 | 6 | 7,
    measured_room_temperature: randFloat(20, 28),
    set_room_temperature: randFloat(20, 26),
    communication_status: randInt(0, 1) as 0 | 1,
  };
}

/**
 * Generate randomized IAQStatus data
 */
function generateIAQStatus(): RoomDeviceStatus['iaq'] {
  return {
    pm25: randInt(5, 50),
    noise: randInt(20, 60),
    illuminance: randInt(300, 600),
    temperature: randFloat(20, 28),
    humidity: randInt(40, 70),
    co2: randInt(350, 700),
    online_status: Math.random() > 0.05,
  };
}

/**
 * Generate randomized PresenceSensor data
 */
function generatePresenceSensor(): RoomDeviceStatus['presence_sensor'] {
  const statuses: RoomDeviceStatus['presence_sensor']['status'][] = [
    'active',
    'inactive',
    'error',
  ];
  return {
    presence_state: Math.random() > 0.3,
    sensitivity: randInt(1, 10),
    status: statuses[randInt(0, statuses.length - 1)],
  };
}

/**
 * Generate randomized OccupancyStatus data
 */
function generateOccupancyStatus(): RoomDeviceStatus['occupancy_status'] {
  return randInt(0, 3) as 0 | 1 | 2 | 3;
}

/**
 * Generate randomized device status for a room
 */
function generateRoomDeviceStatus(): RoomDeviceStatus {
  return {
    fcu: generateFCUStatus(),
    iaq: generateIAQStatus(),
    presence_sensor: generatePresenceSensor(),
    occupancy_status: generateOccupancyStatus(),
  };
}

/**
 * Base room definitions — expanded to match heatmap expectations
 * Generate rooms for multiple floors with proper numbering
 */
const baseRooms: { roomId: string; roomType: Room['roomType'] }[] = [];

// Generate rooms for floors 1-14, with 30 rooms per floor
for (let floor = 1; floor <= 14; floor++) {
  for (let room = 1; room <= 30; room++) {
    const roomNum = room.toString().padStart(2, '0');
    const roomId = `${floor}${roomNum}`;

    // Assign room types in a pattern
    const roomTypes: Room['roomType'][] = ['Standard', 'Deluxe', 'Suite', 'Family', 'Executive'];
    const roomType = roomTypes[(floor + room) % roomTypes.length];

    baseRooms.push({ roomId, roomType });
  }
}

/**
 * Group rooms by floors - generate all 14 floors
 */
const floors: { level: number; rooms: { roomId: string; roomType: Room['roomType'] }[] }[] = [];
for (let floorLevel = 1; floorLevel <= 14; floorLevel++) {
  floors.push({
    level: floorLevel,
    rooms: baseRooms.filter((r) => r.roomId.startsWith(floorLevel.toString())),
  });
}

/**
 * Generate mock time series data with 1-minute intervals
 * @param startDate inclusive Date
 * @param endDate inclusive Date
 */
export function generateMockHotelData(
  startDate: Date,
  endDate: Date
): TimestampedRoomData[] {
  const data: TimestampedRoomData[] = [];
  let currentTime = startDate.getTime();
  const endTime = endDate.getTime();

  // Track previous values for each room
  const roomStateMap: Record<string, IAQStatus> = {};

  while (currentTime <= endTime) {
    const timestampISO = new Date(currentTime).toISOString();

    const floorData = floors.map((floor) => ({
      level: floor.level,
      rooms: floor.rooms.map((room) => {
        const prev = roomStateMap[room.roomId] ?? {
          co2: randInt(400, 600),
          temperature: randFloat(22, 26),
          humidity: randInt(45, 55),
          pm25: randInt(8, 15),
          noise: randInt(30, 45),
          illuminance: randInt(350, 450),
          pmv: randFloat(-0.3, 0.3),
          online_status: true,
        };

        // Simulate drift using previous + noise
        const next: IAQStatus = {
          co2: Math.max(350, Math.min(1200, prev.co2 + randInt(-15, 15))),
          temperature: Math.max(18, Math.min(30, (prev.temperature ?? 24) + randFloat(-0.3, 0.3))),
          humidity: Math.max(30, Math.min(80, (prev.humidity ?? 50) + randInt(-3, 3))),
          pm25: Math.max(2, Math.min(70, (prev.pm25 ?? 10) + randInt(-3, 3))),
          noise: Math.max(20, Math.min(80, (prev.noise ?? 35) + randInt(-4, 4))),
          illuminance: Math.max(100, Math.min(700, (prev.illuminance ?? 400) + randInt(-25, 25))),
          online_status: Math.random() > 0.02, // small chance to go offline
        };

        // Store state for next timestamp
        roomStateMap[room.roomId] = next;

        return {
          roomId: room.roomId,
          roomType: room.roomType,
          devices: {
            iaq: next,
            fcu: generateFCUStatus(),
            presence_sensor: generatePresenceSensor(),
            occupancy_status: generateOccupancyStatus(),
          },
        };
      }),
    }));

    data.push({
      timestamp: timestampISO,
      floors: floorData,
    });

    currentTime += 60000; // increment by 1 minute
  }

  return data;
}
