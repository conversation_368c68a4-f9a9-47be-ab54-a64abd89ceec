import { TimestampedRoomData } from '@/building-mock-data-generator';

// Types
export interface ChartDataPoint {
  time: Date;
  left: number | null;
  right: number | null;
  occupancy: 0 | 1 | 2 | 3;
}

export type OccupancyStatus = 0 | 1 | 2 | 3;

// Constants
const SIX_HOUR_INTERVAL = 6 * 60 * 60 * 1000; // 6 hours in milliseconds
const THREE_HOUR_TOLERANCE = 3 * 60 * 60 * 1000; // 3 hours in milliseconds

/**
 * Get occupancy information including label and color for a given status
 */
export const getOccupancyInfo = (status: OccupancyStatus) => {
  switch (status) {
    case 0:
      return { label: 'Vacant', color: 'rgba(34, 197, 94, 0.2)' }; // Green - room is empty
    case 1:
      return { label: 'Low Occupancy', color: 'rgba(34, 197, 94, 0.1)' }; // Light green
    case 2:
      return { label: 'Medium Occupancy', color: 'rgba(251, 191, 36, 0.2)' }; // Yellow/amber
    case 3:
      return { label: 'High Occupancy', color: 'rgba(239, 68, 68, 0.2)' }; // Red - room is full
    default:
      return { label: 'Unknown', color: 'rgba(156, 163, 175, 0.2)' }; // Gray
  }
};

/**
 * Filter and extract room data within date range
 */
export const filterRoomData = (
  data: TimestampedRoomData[],
  roomId: string,
  dateRange: { startDate: Date; endDate: Date },
  metricLeft: keyof TimestampedRoomData['floors'][0]['rooms'][0]['devices']['iaq'],
  metricRight: keyof TimestampedRoomData['floors'][0]['rooms'][0]['devices']['iaq']
): ChartDataPoint[] => {
  return data
    .filter(
      (entry) =>
        new Date(entry.timestamp) >= dateRange.startDate &&
        new Date(entry.timestamp) <= dateRange.endDate
    )
    .map((entry) => {
      const roomData = entry.floors
        .flatMap((f) => f.rooms)
        .find((r) => r.roomId === roomId);

      return roomData
        ? {
            time: new Date(entry.timestamp),
            left: roomData.devices.iaq[metricLeft] ?? null,
            right: roomData.devices.iaq[metricRight] ?? null,
            occupancy: roomData.devices.occupancy_status,
          }
        : null;
    })
    .filter(Boolean) as ChartDataPoint[];
};

/**
 * Sample data at 6-hour intervals
 */
export const sampleDataAtSixHourIntervals = (
  allData: ChartDataPoint[],
  dateRange: { startDate: Date; endDate: Date }
): ChartDataPoint[] => {
  const filteredData: ChartDataPoint[] = [];

  if (allData.length === 0) {
    return filteredData;
  }

  // Start from the first data point's time, rounded to the nearest 6-hour mark
  const startTime = new Date(dateRange.startDate);
  startTime.setHours(Math.floor(startTime.getHours() / 6) * 6, 0, 0, 0);

  let currentTime = startTime.getTime();
  const endTime = dateRange.endDate.getTime();

  while (currentTime <= endTime) {
    // Find the closest data point to this 6-hour mark (within ±3 hours)
    const targetTime = new Date(currentTime);
    const closestDataPoint = allData.reduce((closest, current) => {
      const currentDiff = Math.abs(current.time.getTime() - currentTime);
      const closestDiff = Math.abs(closest.time.getTime() - currentTime);
      return currentDiff < closestDiff ? current : closest;
    });

    // Only include if within 3 hours of the target time
    const timeDiff = Math.abs(closestDataPoint.time.getTime() - currentTime);
    if (timeDiff <= THREE_HOUR_TOLERANCE) {
      filteredData.push({
        time: targetTime, // Use the 6-hour mark time for consistent spacing
        left: closestDataPoint.left,
        right: closestDataPoint.right,
        occupancy: closestDataPoint.occupancy,
      });
    }

    currentTime += SIX_HOUR_INTERVAL;
  }

  return filteredData;
};

/**
 * Format time labels for 6-hour intervals
 */
export const formatTimeLabels = (data: ChartDataPoint[]): string[] => {
  return data.map((d) =>
    d.time.toLocaleString('en-GB', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  );
};

/**
 * Debug logging for chart data
 */
export const logChartDebugInfo = (
  roomId: string,
  totalDataEntries: number,
  allFilteredDataLength: number,
  sixHourSampledDataLength: number,
  dateRange: { startDate: Date; endDate: Date },
  sampleData: ChartDataPoint[]
): void => {
  console.log('DualAxisLineChart Debug:', {
    roomId,
    totalDataEntries,
    allFilteredDataLength,
    sixHourSampledDataLength,
    dateRange,
    sampleSixHourData: sampleData.slice(0, 3),
    sixHourIntervalMs: SIX_HOUR_INTERVAL,
    occupancyData: sampleData.map(d => ({
      time: d.time.toISOString(),
      occupancy: d.occupancy,
      occupancyInfo: getOccupancyInfo(d.occupancy)
    }))
  });
};
